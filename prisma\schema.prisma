datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String

  parties   PartyUser[]
  scores    Score[]
  teamUsers TeamUser[]
}

model Party {
  id          Int         @id @default(autoincrement())
  name        String
  description String?
  location    String?
  date        DateTime
  image       String?

  participants PartyUser[]
  games        PartyGame[]
}

model Game {
  id          Int         @id @default(autoincrement())
  name        String
  description String?
  isTeamBased Boolean     @default(false)  // 👈 Key flag: team-based or individual

  partyGames  PartyGame[]
}

model PartyGame {
  id      Int      @id @default(autoincrement())

  partyId Int
  gameId  Int

  party   Party    @relation(fields: [partyId], references: [id])
  game    Game     @relation(fields: [gameId], references: [id])

  scores  Score[]
  teams   Team[]
}

model Team {
  id          Int         @id @default(autoincrement())
  name        String
  partyGameId Int

  partyGame   PartyGame   @relation(fields: [partyGameId], references: [id])
  members     TeamUser[]
  scores      Score[]
}

model TeamUser {
  id      Int   @id @default(autoincrement())
  teamId  Int
  userId  Int

  team    Team  @relation(fields: [teamId], references: [id])
  user    User  @relation(fields: [userId], references: [id])
}

model Score {
  id          Int      @id @default(autoincrement())
  points      Int
  userId      Int?     // 👈 Nullable because team games might not use individual scoring
  teamId      Int?     // 👈 Nullable because individual games might not use teams
  partyGameId Int

  user        User?     @relation(fields: [userId], references: [id])
  team        Team?     @relation(fields: [teamId], references: [id])
  partyGame   PartyGame @relation(fields: [partyGameId], references: [id])
}

model PartyUser {
  id      Int   @id @default(autoincrement())
  userId  Int
  partyId Int

  user    User  @relation(fields: [userId], references: [id])
  party   Party @relation(fields: [partyId], references: [id])
}
