import NextAuth from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
// You can also use Google, GitHub, etc. providers

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // Replace with Prisma query to check user in your DB
        if (
          credentials?.email === "<EMAIL>" &&
          credentials?.password === "password123"
        ) {
          return { id: "1", name: "Test User", email: "<EMAIL>" }
        }
        return null
      }
    })
  ],
  pages: {
    signIn: "/auth/signin", // optional custom signin page
  },
  session: {
    strategy: "jwt",
  },
})

export { handler as GET, handler as POST }
